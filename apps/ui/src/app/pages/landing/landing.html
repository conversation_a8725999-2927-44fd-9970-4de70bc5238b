<div class="bg-base-100 text-base-content">
  <!-- Simplified Header -->
  <div [class]="isScrolled ? 'bg-white/95 backdrop-blur-md shadow-lg' : 'simplified-header'"
       class="fixed top-0 left-0 right-0 z-50 transition-all duration-300">
    <div class="flex justify-between items-center p-6">
      <div [class]="isScrolled ? 'text-primary' : 'text-white logo'"
           class="text-3xl font-bold transition-colors duration-300">
        Worthy Freshers
      </div>
      <div class="p-2 transition-all duration-300">
        <a [class]="isScrolled ? 'btn-primary text-white' : 'btn-primary text-white border-white hover:bg-white hover:text-primary'"
           class="btn btn-sm transition-all duration-300"
           routerLink="/auth/login">
          <span class="icon-[heroicons--user] mr-2"></span>
          Login
        </a>
      </div>
    </div>
  </div>

  <!-- <PERSON> Carousel Section -->
  <div #heroSection class="carousel w-full relative overflow-x-hidden" id="home">
    <div class="carousel-item relative w-full flex-shrink-0" id="slide1">
      <div class="hero min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900"
           style="background-image: linear-gradient(rgba(30, 58, 138, 0.8), rgba(67, 56, 202, 0.8)), url(https://images.unsplash.com/photo-1551434678-e076c223a692?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=2850&q=80);">
        <div class="hero-content text-center text-white px-4">
          <div class="max-w-4xl animate-fade-in-up">
            <h1
              class="mb-6 text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold leading-tight bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
              Excellent IT Services For Your Success
            </h1>
            <p class="mb-8 text-lg sm:text-xl lg:text-2xl text-blue-100 max-w-3xl mx-auto leading-relaxed">
              Transform your business with cutting-edge technology solutions. We deliver innovative IT
              services that drive growth, enhance efficiency, and secure your digital future.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
              <button (click)="scrollToSection('services')"
                      class="btn btn-primary btn-lg px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg hover:scale-105 transition-all duration-300 shadow-lg">
                <span class="icon-[heroicons--rocket-launch] mr-2"></span>
                Explore Services
              </button>
              <button (click)="scrollToSection('contact')"
                      class="btn btn-outline btn-lg px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg text-white border-white hover:bg-white hover:text-primary transition-all duration-300">
                <span class="icon-[heroicons--phone] mr-2"></span>
                Contact Us
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="absolute flex justify-between transform -translate-y-1/2 left-5 right-5 top-1/2 z-10">
        <button (click)="prevSlide()"
                class="btn btn-circle btn-ghost text-white hover:bg-white hover:text-primary transition-all duration-300">
          <span class="icon-[heroicons--chevron-left] text-2xl"></span>
        </button>
        <button (click)="nextSlide()"
                class="btn btn-circle btn-ghost text-white hover:bg-white hover:text-primary transition-all duration-300">
          <span class="icon-[heroicons--chevron-right] text-2xl"></span>
        </button>
      </div>
    </div>

    <div class="carousel-item relative w-full flex-shrink-0" id="slide2">
      <div class="hero min-h-screen bg-gradient-to-br from-emerald-900 via-teal-900 to-cyan-900"
           style="background-image: linear-gradient(rgba(6, 78, 59, 0.8), rgba(15, 118, 110, 0.8)), url(https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=2850&q=80);">
        <div class="hero-content text-center text-white">
          <div class="max-w-4xl animate-fade-in-up">
            <h1
              class="mb-6 text-6xl lg:text-7xl font-bold leading-tight bg-gradient-to-r from-white to-emerald-200 bg-clip-text text-transparent">
              Innovative Solutions Built for Tomorrow
            </h1>
            <p class="mb-8 text-xl lg:text-2xl text-emerald-100 max-w-3xl mx-auto leading-relaxed">
              Experience the power of modern technology with our comprehensive suite of services. From web
              development to cloud solutions, we're your trusted technology partner.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
              <button (click)="scrollToSection('about')"
                      class="btn btn-primary btn-lg px-8 py-4 text-lg hover:scale-105 transition-all duration-300 shadow-lg">
                <span class="icon-[heroicons--light-bulb] mr-2"></span>
                Learn More
              </button>
              <button (click)="scrollToSection('products')"
                      class="btn btn-outline btn-lg px-8 py-4 text-lg text-white border-white hover:bg-white hover:text-primary transition-all duration-300">
                <span class="icon-[heroicons--building-office] mr-2"></span>
                Our Clients
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="absolute flex justify-between transform -translate-y-1/2 left-5 right-5 top-1/2 z-10">
        <button (click)="prevSlide()"
                class="btn btn-circle btn-ghost text-white hover:bg-white hover:text-primary transition-all duration-300">
          <span class="icon-[heroicons--chevron-left] text-2xl"></span>
        </button>
        <button (click)="nextSlide()"
                class="btn btn-circle btn-ghost text-white hover:bg-white hover:text-primary transition-all duration-300">
          <span class="icon-[heroicons--chevron-right] text-2xl"></span>
        </button>
      </div>
    </div>

    <div class="carousel-item relative w-full flex-shrink-0" id="slide3">
      <div class="hero min-h-screen bg-gradient-to-br from-violet-900 via-purple-900 to-fuchsia-900"
           style="background-image: linear-gradient(rgba(76, 29, 149, 0.8), rgba(147, 51, 234, 0.8)), url(https://images.unsplash.com/photo-1519389950473-47ba0277781c?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=2850&q=80);">
        <div class="hero-content text-center text-white">
          <div class="max-w-4xl animate-fade-in-up">
            <h1
              class="mb-6 text-6xl lg:text-7xl font-bold leading-tight bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent">
              Your Digital Transformation Partner
            </h1>
            <p class="mb-8 text-xl lg:text-2xl text-purple-100 max-w-3xl mx-auto leading-relaxed">
              Join thousands of satisfied clients who trust us with their digital journey. We combine
              expertise, innovation, and dedication to deliver exceptional results.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
              <button (click)="scrollToSection('services')"
                      class="btn btn-primary btn-lg px-8 py-4 text-lg hover:scale-105 transition-all duration-300 shadow-lg">
                <span class="icon-[heroicons--arrow-right] mr-2"></span>
                Get Started
              </button>
              <button (click)="scrollToSection('testimonials')"
                      class="btn btn-outline btn-lg px-8 py-4 text-lg text-white border-white hover:bg-white hover:text-primary transition-all duration-300">
                <span class="icon-[heroicons--star] mr-2"></span>
                Testimonials
              </button>
            </div>
          </div>
        </div>
      </div>
      <div class="absolute flex justify-between transform -translate-y-1/2 left-5 right-5 top-1/2 z-10">
        <button (click)="prevSlide()"
                class="btn btn-circle btn-ghost text-white hover:bg-white hover:text-primary transition-all duration-300">
          <span class="icon-[heroicons--chevron-left] text-2xl"></span>
        </button>
        <button (click)="nextSlide()"
                class="btn btn-circle btn-ghost text-white hover:bg-white hover:text-primary transition-all duration-300">
          <span class="icon-[heroicons--chevron-right] text-2xl"></span>
        </button>
      </div>
    </div>

    <!-- Slide Indicators -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2 z-10">
      <button
        (click)="goToSlide(i)"
        *ngFor="let slide of [0,1,2]; let i = index"
        [class]="'w-3 h-3 rounded-full transition-all duration-300 ' + (currentSlide === i ? 'bg-white' : 'bg-white bg-opacity-50')"
      ></button>
    </div>
  </div>

  <!-- Services Section -->
  <section class="py-20 bg-gradient-to-br from-base-100 to-base-200" id="services">
    <div class="container mx-auto px-4">
      <div class="text-center mb-12">
        <h2 class="text-4xl font-bold">Services We Offer</h2>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div class="card bg-base-200 shadow-xl">
          <div class="card-body items-center text-center">
            <h2 class="card-title">Project Tracking</h2>
            <p>Monitor tasks, milestones, and deadlines in real time to keep projects on track and teams aligned.</p>
          </div>
        </div>
        <div class="card bg-base-200 shadow-xl">
          <div class="card-body items-center text-center">
            <h2 class="card-title">Training Contributions</h2>
            <p>Measure and showcase the impact of learning sessions, mentorship, and knowledge-sharing.</p>
          </div>
        </div>
        <div class="card bg-base-200 shadow-xl">
          <div class="card-body items-center text-center">
            <h2 class="card-title">Institutes & Faculty Connect</h2>
            <p>Strengthen collaboration between institutes and faculty for impactful learning and student growth.</p>
          </div>
        </div>
        <div class="card bg-base-200 shadow-xl">
          <div class="card-body items-center text-center">
            <h2 class="card-title">Parent Dashboard</h2>
            <p>Stay informed about your child’s progress, performance, and learning activities in one place.</p>
          </div>
        </div>
        <div class="card bg-base-200 shadow-xl">
          <div class="card-body items-center text-center">
            <h2 class="card-title">Placement Analytics</h2>
            <p>Gain insights into placement trends, success rates, and career outcomes to boost employability.</p>
          </div>
        </div>
        <div class="card bg-base-200 shadow-xl">
          <div class="card-body items-center text-center">
            <h2 class="card-title">Smart Notifications</h2>
            <p>Stay updated with personalized alerts on classes, projects, placements, and announcements.</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Why Choose Us Section -->
  <section class="py-20 bg-base-200" id="about">
    <div class="container mx-auto px-4">
      <div class="text-center mb-12">
        <h4 class="text-xl font-bold text-primary">Why choose Us</h4>
        <h2 class="text-4xl font-bold">We Create Result-Oriented Dynamic Applications</h2>
      </div>
      <div class="hero">
        <div class="hero-content flex-col lg:flex-row">
          <img
            class="max-w-sm rounded-lg shadow-2xl"
            src="https://images.unsplash.com/photo-1521737604893-d14cc237f11d?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=1350&q=80" />
          <div>
            <div class="flex items-start mb-4">
              <div class="mr-4">
                <i class="fas fa-cogs text-3xl text-primary"></i>
              </div>
              <div>
                <h4 class="font-bold text-xl">First Growing Process</h4>
                <p>A structured first step towards sustainable growth and success.</p>
              </div>
            </div>
            <div class="flex items-start mb-4">
              <div class="mr-4">
                <i class="fas fa-code text-3xl text-primary"></i>
              </div>
              <div>
                <h4 class="font-bold text-xl">Clean Code</h4>
                <p>Clean, reliable, and future-ready code for every solution.</p>
              </div>
            </div>
            <div class="flex items-start">
              <div class="mr-4">
                <i class="fas fa-file-alt text-3xl text-primary"></i>
              </div>
              <div>
                <h4 class="font-bold text-xl">Well Documentation</h4>
                <p>Comprehensive documentation for clarity, continuity, and confidence.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Customers Section -->
  <section class="py-20" id="products">
    <div class="container mx-auto px-4">
      <div class="text-center mb-12">
        <h6 class="font-bold">Trusted By Over 1500</h6>
        <h2 class="text-4xl font-bold">Our Customers</h2>
      </div>
      <div class="flex justify-center items-center flex-wrap">
        <img
          alt="Mallareddy University - Educational partner"
          class="h-16 sm:h-20 mx-2 sm:mx-4 my-2 object-contain hover:scale-105 transition-transform duration-300"
          height="80"
          loading="lazy"
          src="https://static.wixstatic.com/media/6685d7_686c085bd3864f3e8ae805bfc3407db3~mv2.png/v1/fill/w_235,h_220,al_c,q_85,usm_0.66_1.00_0.01,enc_avif,quality_auto/MRU-Logo2.png"
          width="80">
        <img alt="Aditya University - Educational partner"
             class="h-16 sm:h-20 mx-2 sm:mx-4 my-2 object-contain hover:scale-105 transition-transform duration-300"
             height="80"
             loading="lazy"
             src="https://adityauniversity.in/static/media/au.f652eed91d8ba58a4968.webp"
             width="80">
        <img alt="GITAM University - Educational partner"
             class="h-16 sm:h-20 mx-2 sm:mx-4 my-2 object-contain hover:scale-105 transition-transform duration-300"
             height="80"
             loading="lazy"
             src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQsriUcrrpLREt5CqLA-iMSnmdK_AMbLvToeQ&s"
             width="80">
        <img alt="Guru Nanak University - Educational partner"
             class="h-16 sm:h-20 mx-2 sm:mx-4 my-2 object-contain hover:scale-105 transition-transform duration-300"
             height="80"
             loading="lazy"
             src="https://gnuindia.org/images/GNU-logo.png"
             width="80">
      </div>
    </div>
  </section>

  <!-- Stats Section -->
  <div class="stats shadow w-full rounded-none px-5 bg-gradient-to-r from-primary to-secondary text-primary-content">
    <div class="stat">
      <div class="stat-value text-4xl font-bold">24+</div>
      <div class="stat-desc text-primary-content/80 font-medium">Years Online Business</div>
    </div>
    <div class="stat">
      <div class="stat-value text-4xl font-bold">250+</div>
      <div class="stat-desc text-primary-content/80 font-medium">Working Employees</div>
    </div>
    <div class="stat">
      <div class="stat-value text-4xl font-bold">4500+</div>
      <div class="stat-desc text-primary-content/80 font-medium">Complete Projects</div>
    </div>
    <div class="stat">
      <div class="stat-value text-4xl font-bold">3000+</div>
      <div class="stat-desc text-primary-content/80 font-medium">Happy Customers</div>
    </div>
  </div>

  <!-- Testimonials Section -->
  <section class="py-20 bg-base-100" id="testimonials">
    <div class="container mx-auto px-4">
      <div class="text-center mb-16">
        <h6 class="text-primary font-semibold text-lg mb-2 uppercase tracking-wide">Testimonials</h6>
        <h2
          class="text-5xl lg:text-6xl font-bold mb-4 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
          What Our Clients Say
        </h2>
        <p class="text-xl text-base-content/70 max-w-3xl mx-auto">
          Hear from educational institutions who have transformed their operations with our solutions
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <div *ngFor="let testimonial of testimonials; trackBy: trackByTestimonial"
             [attr.aria-label]="'Testimonial from ' + testimonial.name"
             class="card bg-base-200 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1"
             role="article">
          <div class="card-body p-6 sm:p-8">
            <div class="flex items-center mb-6">
              <div class="avatar">
                <div class="w-14 h-14 sm:w-16 sm:h-16 rounded-full">
                  <img [alt]="'Profile photo of ' + testimonial.name"
                       [src]="testimonial.image"
                       class="rounded-full object-cover"
                       height="64"
                       loading="lazy"
                       width="64" />
                </div>
              </div>
              <div class="ml-4">
                <h3 class="font-bold text-lg">{{ testimonial.name }}</h3>
                <p class="text-base-content/70 text-sm sm:text-base">{{ testimonial.position }}</p>
              </div>
            </div>

            <div [attr.aria-label]="testimonial.rating + ' out of 5 stars'" class="flex mb-4" role="img">
              <span *ngFor="let star of [1,2,3,4,5]"
                    [attr.aria-hidden]="true"
                    [class]="'icon-[heroicons--star] w-5 h-5 ' + (star <= testimonial.rating ? 'text-yellow-400' : 'text-gray-300')">
              </span>
            </div>

            <blockquote class="text-base-content/80 leading-relaxed italic text-sm sm:text-base">
              "{{ testimonial.text }}"
            </blockquote>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- FAQ Section -->
  <section class="py-20 bg-base-200" id="faq">
    <div class="container mx-auto px-4">
      <div class="text-center mb-16">
        <h6 class="text-primary font-semibold text-lg mb-2 uppercase tracking-wide">FAQ</h6>
        <h2
          class="text-5xl lg:text-6xl font-bold mb-4 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
          Frequently Asked Questions
        </h2>
        <p class="text-xl text-base-content/70 max-w-3xl mx-auto">
          Find answers to common questions about our services and solutions
        </p>
      </div>

      <div class="max-w-4xl mx-auto">
        <div *ngFor="let faq of faqs; let i = index; trackBy: trackByFaq"
             [attr.aria-labelledby]="'faq-question-' + i"
             class="collapse collapse-plus bg-base-100 mb-4 shadow-lg"
             role="region">
          <input [checked]="faq.isOpen"
                 [id]="'faq-' + i"
                 [name]="'faq-accordion'"
                 class="sr-only"
                 type="radio" />
          <div
            (click)="toggleFaq(i)"
            (keydown.enter)="toggleFaq(i)"
            (keydown.space)="toggleFaq(i)"
            [attr.aria-controls]="'faq-answer-' + i"
            [attr.aria-expanded]="faq.isOpen"
            [id]="'faq-question-' + i"
            class="collapse-title text-lg sm:text-xl font-medium cursor-pointer hover:text-primary transition-colors duration-300"
            role="button"
            tabindex="0">
            {{ faq.question }}
          </div>
          <div [attr.aria-labelledby]="'faq-question-' + i"
               [id]="'faq-answer-' + i"
               class="collapse-content"
               role="region">
            <p class="text-base-content/80 leading-relaxed text-sm sm:text-base">{{ faq.answer }}</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Contact Section -->
  <section class="py-20 bg-base-100" id="contact">
    <div class="container mx-auto px-4">
      <div class="text-center mb-16">
        <h6 class="text-primary font-semibold text-lg mb-2 uppercase tracking-wide">Get In Touch</h6>
        <h2
          class="text-5xl lg:text-6xl font-bold mb-4 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
          Contact Us
        </h2>
        <p class="text-xl text-base-content/70 max-w-3xl mx-auto">
          Ready to transform your educational institution? Let's discuss how we can help you achieve your goals
        </p>
      </div>

      <div class="hero">
        <div class="hero-content flex-col lg:flex-row-reverse gap-12">
          <div class="text-center lg:text-left flex-1">
            <img
              alt="Professional team discussing technology solutions in a modern office environment"
              class="max-w-lg rounded-2xl shadow-2xl w-full object-cover"
              height="400"
              loading="lazy"
              src="https://images.unsplash.com/photo-1596524430615-b46475ddff6e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=1350&q=80"
              width="600" />

            <div class="mt-8 space-y-6">
              <div class="flex items-center">
                <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
                  <span class="icon-[heroicons--phone] text-white text-xl"></span>
                </div>
                <div>
                  <h4 class="font-bold text-lg">Phone</h4>
                  <p class="text-base-content/70">+91 9949858569</p>
                </div>
              </div>

              <div class="flex items-center">
                <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
                  <span class="icon-[heroicons--envelope] text-white text-xl"></span>
                </div>
                <div>
                  <h4 class="font-bold text-lg">Email</h4>
                  <p class="text-base-content/70"><EMAIL></p>
                </div>
              </div>

              <div class="flex items-center">
                <div class="w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
                  <span class="icon-[heroicons--map-pin] text-white text-xl"></span>
                </div>
                <div>
                  <h4 class="font-bold text-lg">Address</h4>
                  <p class="text-base-content/70">Plot No: 49B, H-63/4,<br>4th Floor Madhapur, Hyderabad,<br>Telangana
                    500034</p>
                </div>
              </div>
            </div>
          </div>

          <div class="card flex-shrink-0 w-full max-w-lg shadow-2xl bg-base-200 border border-base-300">
            <form (ngSubmit)="onSubmit()"
                  [formGroup]="contactForm"
                  aria-label="Contact form"
                  class="card-body p-6 sm:p-8"
                  role="form">
              <h3 class="text-xl sm:text-2xl font-bold mb-6 text-center">Send us a Message</h3>

              <!-- Success Message -->
              <div *ngIf="formSubmitted" class="alert alert-success mb-6">
                <span class="icon-[heroicons--check-circle]"></span>
                <span>Thank you! Your message has been sent successfully.</span>
              </div>

              <div class="form-control mb-4">
                <label class="label pb-1">
                  <span class="label-text font-medium text-base">Your Name</span>
                </label>
                <input
                  [attr.aria-describedby]="getFieldError('name') ? 'name-error' : null"
                  [attr.aria-invalid]="getFieldError('name') ? 'true' : 'false'"
                  [class.input-error]="getFieldError('name')"
                  autocomplete="name"
                  class="input input-bordered w-full focus:input-primary transition-colors duration-300"
                  formControlName="name"
                  id="contact-name"
                  placeholder="Enter your full name"
                  type="text" />
                <div *ngIf="getFieldError('name')" class="label pt-1">
                  <span class="label-text-alt text-error">{{ getFieldError('name') }}</span>
                </div>
              </div>

              <div class="form-control mb-4">
                <label class="label pb-1">
                  <span class="label-text font-medium text-base">Your Email</span>
                </label>
                <input
                  [class.input-error]="getFieldError('email')"
                  autocomplete="email"
                  class="input input-bordered w-full focus:input-primary transition-colors duration-300"
                  formControlName="email"
                  id="contact-email"
                  placeholder="Enter your email address"
                  type="email" />
                <div *ngIf="getFieldError('email')" class="label pt-1">
                  <span class="label-text-alt text-error">{{ getFieldError('email') }}</span>
                </div>
              </div>

              <div class="form-control mb-4">
                <label class="label pb-1">
                  <span class="label-text font-medium text-base">Subject</span>
                </label>
                <input
                  [class.input-error]="getFieldError('subject')"
                  class="input input-bordered w-full focus:input-primary transition-colors duration-300"
                  formControlName="subject"
                  id="contact-subject"
                  placeholder="What is this about?"
                  type="text" />
                <div *ngIf="getFieldError('subject')" class="label pt-1">
                  <span class="label-text-alt text-error">{{ getFieldError('subject') }}</span>
                </div>
              </div>

              <div class="form-control mb-6">
                <label class="label pb-1">
                  <span class="label-text font-medium text-base">Your Message</span>
                </label>
                <textarea
                  [class.textarea-error]="getFieldError('message')"
                  class="textarea textarea-bordered h-32 w-full focus:textarea-primary transition-colors duration-300"
                  formControlName="message"
                  id="contact-message"
                  placeholder="Tell us more about your requirements..."></textarea>
                <div *ngIf="getFieldError('message')" class="label pt-1">
                  <span class="label-text-alt text-error">{{ getFieldError('message') }}</span>
                </div>
              </div>

              <div class="form-control mt-8">
                <button
                  [class.loading]="isFormSubmitting"
                  [disabled]="isFormSubmitting"
                  class="btn btn-primary btn-lg"
                  type="submit">
                  <span *ngIf="!isFormSubmitting" class="icon-[heroicons--paper-airplane] mr-2"></span>
                  {{ isFormSubmitting ? 'Sending...' : 'Send Message' }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Footer -->
  <footer class="footer p-10 bg-neutral text-neutral-content flex justify-evenly">
    <div>
      <span class="footer-title">NAVAKARANA TECHNOLOGIES</span>
      <p class="max-w-96">Navakarana is a leading software service and a solution provider for various industrial
        clients across the globe. We offer innovative, robust and scalable solutions using cutting-edge Technologies
        which will help organization to increase their profit margins, enhance productivity and business operations.</p>
    </div>
    <div>
      <span class="footer-title">Company</span>
      <a class="link link-hover">About Us</a>
      <a class="link link-hover">Carrers</a>
      <a class="link link-hover">Events</a>
      <a class="link link-hover">Contact Us</a>
    </div>
    <div>
      <span class="footer-title">Resources</span>
      <a class="link link-hover">Privacy Policy</a>
      <a class="link link-hover">Terms of Service</a>
      <a class="link link-hover">Refund Policy</a>
      <a class="link link-hover">Disclamier</a>
      <a class="link link-hover">Cookie Policy</a>
    </div>
    <div>
      <span class="footer-title">India</span>
      <p>Plot No: 49B, H-63/4,<br>4th Floor Madhapur,Hyderabad,<br>Telangana 500034</p>
      <p><strong>Email:</strong> <EMAIL></p>
      <p><strong>Phone:</strong> +91 9949858569</p>
    </div>
  </footer>
  <footer class="footer footer-center p-4 bg-base-300 text-base-content">
    <div>
      <p>All Rights Reserved &copy; NAVAKARANA</p>
    </div>
  </footer>

  <!-- Floating Navigation Menu -->
  <div class="fixed bottom-8 left-1/2 transform -translate-x-1/2 z-50 transition-all duration-300">
    <div [class]="isScrolled ? 'bg-white/95 backdrop-blur-md border border-primary/20' : 'floating-nav'"
         class="rounded-full px-6 py-3 shadow-lg transition-all duration-300">
      <div class="flex items-center space-x-4">
        <button (click)="scrollToSection('home')"
                [class]="isScrolled ? 'text-primary hover:bg-primary/10' : 'text-white hover:bg-white/20'"
                class="p-2 rounded-full transition-colors duration-300"
                title="Home">
          <span class="icon-[heroicons--home] text-lg"></span>
        </button>
        <button (click)="scrollToSection('services')"
                [class]="isScrolled ? 'text-primary hover:bg-primary/10' : 'text-white hover:bg-white/20'"
                class="p-2 rounded-full transition-colors duration-300"
                title="Services">
          <span class="icon-[heroicons--cog-6-tooth] text-lg"></span>
        </button>
        <button (click)="scrollToSection('about')"
                [class]="isScrolled ? 'text-primary hover:bg-primary/10' : 'text-white hover:bg-white/20'"
                class="p-2 rounded-full transition-colors duration-300"
                title="About">
          <span class="icon-[heroicons--information-circle] text-lg"></span>
        </button>
        <button (click)="scrollToSection('products')"
                [class]="isScrolled ? 'text-primary hover:bg-primary/10' : 'text-white hover:bg-white/20'"
                class="p-2 rounded-full transition-colors duration-300"
                title="Clients">
          <span class="icon-[heroicons--building-office] text-lg"></span>
        </button>
        <button (click)="scrollToSection('contact')"
                [class]="isScrolled ? 'text-primary hover:bg-primary/10' : 'text-white hover:bg-white/20'"
                class="p-2 rounded-full transition-colors duration-300"
                title="Contact">
          <span class="icon-[heroicons--phone] text-lg"></span>
        </button>
      </div>
    </div>
  </div>

  <!-- Scroll to Top Button -->
  <button
    (click)="scrollToTop()"
    class="fixed bottom-8 right-8 btn btn-circle btn-primary shadow-lg hover:shadow-xl transition-all duration-300 z-50 hover:scale-110"
    title="Scroll to top">
    <span class="icon-[heroicons--arrow-up] text-xl"></span>
  </button>
</div>
