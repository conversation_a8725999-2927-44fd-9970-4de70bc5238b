import {
  Component,
  ElementRef,
  inject,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  ViewChild,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { RouterLink } from '@angular/router';
import { addDoc, collection, Firestore } from '@angular/fire/firestore';

@Component({
  selector: 'app-landing',
  imports: [CommonModule, ReactiveFormsModule, RouterLink],
  templateUrl: './landing.html',
  styleUrls: ['./landing.css'],
})
export class Landing implements OnInit, OnDestroy {
  @ViewChild('heroSection', { static: false }) heroSection!: ElementRef;
  contactForm: FormGroup;
  currentSlide = 0;
  totalSlides = 3;
  slideInterval: any;
  isFormSubmitting = false;
  formSubmitted = false;
  isScrolled = false;
  // FAQ data
  faqs = [
    {
      question: 'What services do you offer?',
      answer:
        'We offer comprehensive IT services including project tracking, training contributions, institute & faculty connect, parent dashboard, placement analytics, and smart notifications.',
      isOpen: false,
    },
    {
      question: 'How can I get started?',
      answer:
        'You can get started by contacting us through our contact form or exploring our services section to learn more about what we offer.',
      isOpen: false,
    },
    {
      question: 'Do you provide support for educational institutions?',
      answer:
        'Yes, we specialize in providing technology solutions for educational institutions, including student management, placement tracking, and faculty collaboration tools.',
      isOpen: false,
    },
    {
      question: 'What makes your solutions different?',
      answer:
        'Our solutions are result-oriented, built with clean code, well-documented, and designed specifically for the education sector with over 24 years of experience.',
      isOpen: false,
    },
  ];
  // Testimonials data
  testimonials = [
    {
      name: 'Dr. Rajesh Kumar',
      position: 'Dean, Mallareddy University',
      image:
        'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      rating: 5,
      text: 'Worthy Freshers has transformed how we manage our student data and placement activities. The platform is intuitive and highly effective.',
    },
    {
      name: 'Prof. Anitha Sharma',
      position: 'HOD, Aditya University',
      image:
        'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      rating: 5,
      text: 'The training contribution tracking feature has helped us measure the impact of our faculty development programs effectively.',
    },
    {
      name: 'Mr. Suresh Reddy',
      position: 'Placement Officer, GITAM',
      image:
        'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      rating: 5,
      text: 'The placement analytics dashboard provides valuable insights that have improved our placement success rate by 40%.',
    },
  ];
  private firestore: Firestore = inject(Firestore);

  constructor(private fb: FormBuilder) {
    this.contactForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      subject: ['', [Validators.required, Validators.minLength(5)]],
      message: ['', [Validators.required, Validators.minLength(10)]],
    });
  }

  ngOnInit() {
    this.startSlideshow();
    this.setupScrollAnimations();
    this.setupFloatingNavigation();
  }

  ngOnDestroy() {
    if (this.slideInterval) {
      clearInterval(this.slideInterval);
    }
  }

  startSlideshow() {
    this.slideInterval = setInterval(() => {
      this.nextSlide();
    }, 5000);
  }

  nextSlide() {
    this.currentSlide = (this.currentSlide + 1) % this.totalSlides;
    this.goToSlide(this.currentSlide);
  }

  prevSlide() {
    this.currentSlide =
      this.currentSlide === 0 ? this.totalSlides - 1 : this.currentSlide - 1;
    this.goToSlide(this.currentSlide);
  }

  goToSlide(index: number) {
    this.currentSlide = index;
    const carousel = document.querySelector('.carousel');
    const slideElement = document.getElementById(`slide${index + 1}`);
    if (carousel && slideElement) {
      // Use carousel's scrollLeft instead of scrollIntoView to prevent page scrolling
      const slideWidth = slideElement.offsetWidth;
      carousel.scrollTo({
        left: slideWidth * index,
        behavior: 'smooth',
      });
    }
  }

  setupScrollAnimations() {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px',
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-fade-in-up');
        }
      });
    }, observerOptions);

    // Observe all sections
    setTimeout(() => {
      const sections = document.querySelectorAll('section, .stats');
      sections.forEach((section) => observer.observe(section));
    }, 100);
  }

  setupFloatingNavigation() {
    window.addEventListener('scroll', () => {
      const scrollY = window.scrollY;

      // Change header/menu colors when scrolled
      this.isScrolled = scrollY > 100;
    });
  }

  toggleFaq(index: number) {
    this.faqs[index].isOpen = !this.faqs[index].isOpen;
  }

  async onSubmit() {
    if (this.contactForm.valid) {
      this.isFormSubmitting = true;

      try {
        // Add form data to Firestore
        const contactData = {
          ...this.contactForm.value,
          timestamp: new Date(),
          status: 'new',
        };

        const contactsCollection = collection(this.firestore, 'help');
        await addDoc(contactsCollection, contactData);

        this.isFormSubmitting = false;
        this.formSubmitted = true;
        this.contactForm.reset();

        // Reset success message after 5 seconds
        setTimeout(() => {
          this.formSubmitted = false;
        }, 5000);

        console.log('Contact form submitted successfully!');
      } catch (error) {
        console.error('Error submitting contact form:', error);
        this.isFormSubmitting = false;
        // You could add error handling here
      }
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.contactForm.controls).forEach((key) => {
        this.contactForm.get(key)?.markAsTouched();
      });
    }
  }

  getFieldError(fieldName: string): string {
    const field = this.contactForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) return `${fieldName} is required`;
      if (field.errors['email']) return 'Please enter a valid email';
      if (field.errors['minlength']) return `${fieldName} is too short`;
    }
    return '';
  }

  scrollToSection(sectionId: string) {
    const element = document.getElementById(sectionId);
    if (element) {
      const headerHeight = 80; // Approximate height of the fixed header
      const elementPosition = element.offsetTop - headerHeight;

      window.scrollTo({
        top: elementPosition,
        behavior: 'smooth',
      });
    }
  }

  scrollToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  trackByTestimonial(index: number, testimonial: any): string {
    return testimonial.name;
  }

  trackByFaq(index: number, faq: any): string {
    return faq.question;
  }
}
